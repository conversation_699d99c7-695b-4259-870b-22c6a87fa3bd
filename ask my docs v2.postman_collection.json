{"info": {"_postman_id": "6143ac78-aa79-4c25-a11b-43a8abff2ac5", "name": "ask my docs v2", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "24850607", "_collection_link": "https://crazy9-2438.postman.co/workspace/Crazy-Workspace~3ac5f569-a4a4-4748-82e3-cf3765fa7b89/collection/24850607-6143ac78-aa79-4c25-a11b-43a8abff2ac5?action=share&source=collection_link&creator=24850607"}, "item": [{"name": "project", "item": [{"name": "create projects v2", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev}}/v2/projects", "host": ["{{dev}}"], "path": ["v2", "projects"]}}, "response": []}, {"name": "Get All projects", "request": {"method": "GET", "header": [], "url": {"raw": "{{dev}}/projects", "host": ["{{dev}}"], "path": ["projects"]}}, "response": []}]}, {"name": "category", "item": [{"name": "create category v2", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Research Papers\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev}}/v2/projects/:projectId/category", "host": ["{{dev}}"], "path": ["v2", "projects", ":projectId", "category"], "variable": [{"key": "projectId", "value": "4"}]}}, "response": []}, {"name": "list category", "request": {"method": "GET", "header": [], "url": {"raw": "{{dev}}/projects/:projectId/category", "host": ["{{dev}}"], "path": ["projects", ":projectId", "category"], "variable": [{"key": "projectId", "value": "1"}]}}, "response": []}]}, {"name": "document", "item": [{"name": "Upload v2", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "projectId", "value": "4", "type": "text"}, {"key": "file", "type": "file", "src": "/C:/Users/<USER>/Downloads/An_Enhanced_RAIM_Method_for_Satellite-Based_Positi.pdf"}, {"key": "categoryId", "value": "3", "type": "text"}, {"key": "metadata", "value": "{\"info\":\"Research Papers\"}", "type": "text"}]}, "url": {"raw": "{{dev}}/v2/documents/upload", "host": ["{{dev}}"], "path": ["v2", "documents", "upload"]}}, "response": []}, {"name": "get files name by category", "request": {"method": "GET", "header": [], "url": {"raw": "{{local}}/documents/:categoryId", "host": ["{{local}}"], "path": ["documents", ":categoryId"], "variable": [{"key": "categoryId", "value": "3"}]}}, "response": []}]}, {"name": "chat", "item": [{"name": "chat health", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"query\": \"explain me _cap_does_task_list_need_generating  function and who made this chanes\",\r\n    \"projectId\": 1,\r\n    \"categoryId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev}}/chat/health", "host": ["{{dev}}"], "path": ["chat", "health"]}}, "response": []}, {"name": "ollama chat v2", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"query\": \"explain me the topologies used in the paper\",\r\n    \"projectId\": 1,\r\n    \"categoryId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev}}/v2/chat", "host": ["{{dev}}"], "path": ["v2", "chat"]}}, "response": []}]}]}