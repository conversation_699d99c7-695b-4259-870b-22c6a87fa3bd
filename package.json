{"name": "pdf-rag", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "format": "prettier . --write", "check": "pnpm run lint && prettier . --write"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.8", "@tabler/icons-react": "^3.34.1", "@tanstack/react-query": "^5.85.6", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jose": "^6.1.0", "lucide-react": "^0.542.0", "next": "15.5.2", "next-themes": "^0.4.6", "nuqs": "^2.5.2", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "server-only": "^0.0.1", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "zod": "^4.1.5", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5"}}