'use client';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useDocuments } from '@/modules/document';
import { DocumentSchema } from '@/modules/document/schema';
import { formatDistanceToNow } from 'date-fns';
import { ArrowLeft, FileText, Upload } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

function DocumentCard({ document }: { document: DocumentSchema }) {
    return (
        <Card className="transition-shadow hover:shadow-md">
            <CardHeader>
                <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                        <div className="bg-primary/10 rounded-lg p-2">
                            <FileText className="text-primary h-5 w-5" />
                        </div>
                        <div>
                            <CardTitle className="text-lg">{document.name}</CardTitle>
                            <CardDescription>
                                Uploaded {formatDistanceToNow(new Date(document.createdAt), { addSuffix: true })}
                            </CardDescription>
                        </div>
                    </div>
                    <div className="flex items-center space-x-2">
                        <Button size="sm" variant="outline">
                            View
                        </Button>
                        <Button size="sm" variant="outline">
                            Download
                        </Button>
                    </div>
                </div>
            </CardHeader>
        </Card>
    );
}

function DocumentSkeleton() {
    return (
        <Card>
            <CardHeader>
                <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                        <Skeleton className="h-9 w-9 rounded-lg" />
                        <div className="space-y-2">
                            <Skeleton className="h-5 w-32" />
                            <Skeleton className="h-4 w-24" />
                        </div>
                    </div>
                    <div className="flex items-center space-x-2">
                        <Skeleton className="h-8 w-16" />
                        <Skeleton className="h-8 w-20" />
                    </div>
                </div>
            </CardHeader>
        </Card>
    );
}

export default function CategoryDetailPage() {
    const params = useParams();
    const projectId = params.projectId as string;
    const categoryId = params.categoryId as string;

    const { data: documentsData, isLoading, error } = useDocuments(categoryId);

    return (
        <div className="space-y-6 p-6">
            <div className="flex items-center space-x-4">
                <Button variant="outline" size="sm" asChild>
                    <Link href={`/admin/projects/${projectId}`}>
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Project
                    </Link>
                </Button>
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Category Documents</h1>
                    <p className="text-muted-foreground">Manage documents in this category</p>
                </div>
            </div>

            <div className="grid gap-6">
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle>Documents</CardTitle>
                                <CardDescription>Upload and manage PDF documents for RAG processing</CardDescription>
                            </div>
                            <Button asChild>
                                <Link href={`/admin/projects/${projectId}/categories/${categoryId}/upload`}>
                                    <Upload className="mr-2 h-4 w-4" />
                                    Upload Document
                                </Link>
                            </Button>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {error && (
                            <div className="py-8 text-center">
                                <p className="text-destructive">Failed to load documents. Please try again.</p>
                            </div>
                        )}

                        <div className="space-y-4">
                            {isLoading ? (
                                Array.from({ length: 3 }).map((_, i) => <DocumentSkeleton key={i} />)
                            ) : documentsData?.data.documents.length === 0 ? (
                                <div className="py-12 text-center">
                                    <div className="space-y-4">
                                        <div className="bg-muted/50 inline-block rounded-lg p-4">
                                            <FileText className="text-muted-foreground h-8 w-8" />
                                        </div>
                                        <div>
                                            <h3 className="text-lg font-semibold">No documents yet</h3>
                                            <p className="text-muted-foreground mb-4">
                                                Upload your first PDF document to get started.
                                            </p>
                                            <Button asChild>
                                                <Link
                                                    href={`/admin/projects/${projectId}/categories/${categoryId}/upload`}
                                                >
                                                    <Upload className="mr-2 h-4 w-4" />
                                                    Upload Document
                                                </Link>
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                documentsData?.data.documents.map((document) => (
                                    <DocumentCard key={document._id} document={document} />
                                ))
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
