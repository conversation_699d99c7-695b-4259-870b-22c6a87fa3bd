'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import useMutationHelper from '@/hooks/use-mutation-helper';
import { useUploadDocument } from '@/modules/document';
import { UploadDocumentSchema, uploadDocumentSchema } from '@/modules/document/schema';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, FileText, Loader2, Upload, X } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { useForm } from 'react-hook-form';

export default function UploadDocumentPage() {
    const params = useParams();
    const router = useRouter();
    const projectId = params.projectId as string;
    const categoryId = params.categoryId as string;
    const uploadDocumentMutation = useUploadDocument();

    const [selectedFile, setSelectedFile] = useState<File | null>(null);

    const form = useForm<UploadDocumentSchema>({
        resolver: zodResolver(uploadDocumentSchema),
        defaultValues: {
            name: '',
            categoryId,
        },
    });

    useMutationHelper({
        mutation: uploadDocumentMutation,
        onSuccess: () => {
            router.push(`/admin/projects/${projectId}/categories/${categoryId}`);
        },
        successMessage: 'Document uploaded successfully',
        errorMessage: 'Failed to upload document',
        showSuccessToast: true,
        showErrorToast: true,
    });

    const onDrop = useCallback(
        (acceptedFiles: File[]) => {
            const file = acceptedFiles[0];
            if (file) {
                setSelectedFile(file);
                if (!form.getValues('name')) {
                    form.setValue('name', file.name.replace(/\.[^/.]+$/, ''));
                }
            }
        },
        [form],
    );

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        accept: {
            'application/pdf': ['.pdf'],
        },
        maxFiles: 1,
        disabled: uploadDocumentMutation.isPending,
    });

    const removeFile = () => {
        setSelectedFile(null);
        form.setValue('name', '');
    };

    const onSubmit = (data: UploadDocumentSchema) => {
        if (!selectedFile) return;

        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('name', data.name);
        formData.append('categoryId', categoryId);

        uploadDocumentMutation.mutate(formData);
    };

    return (
        <div className="space-y-6 p-6">
            <div className="flex items-center space-x-4">
                <Button variant="outline" size="sm" asChild>
                    <Link href={`/admin/projects/${projectId}/categories/${categoryId}`}>
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Category
                    </Link>
                </Button>
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Upload Document</h1>
                    <p className="text-muted-foreground">Upload a PDF document for RAG processing</p>
                </div>
            </div>

            <div className="max-w-2xl">
                <Card>
                    <CardHeader>
                        <CardTitle>Document Upload</CardTitle>
                        <CardDescription>Select a PDF file and provide a name for your document.</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {/* File Upload Area */}
                        <div
                            {...getRootProps()}
                            className={`cursor-pointer rounded-lg border-2 border-dashed p-8 text-center transition-colors ${
                                isDragActive
                                    ? 'border-primary bg-primary/5'
                                    : selectedFile
                                      ? 'border-green-500 bg-green-50 dark:bg-green-950/20'
                                      : 'border-muted-foreground/25 hover:border-muted-foreground/50'
                            } ${uploadDocumentMutation.isPending ? 'pointer-events-none opacity-50' : ''}`}
                        >
                            <input {...getInputProps()} />

                            {selectedFile ? (
                                <div className="space-y-4">
                                    <div className="flex items-center justify-center space-x-2">
                                        <FileText className="h-8 w-8 text-green-600" />
                                        <div className="text-left">
                                            <p className="font-medium">{selectedFile.name}</p>
                                            <p className="text-muted-foreground text-sm">
                                                {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                                            </p>
                                        </div>
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                removeFile();
                                            }}
                                            disabled={uploadDocumentMutation.isPending}
                                        >
                                            <X className="h-4 w-4" />
                                        </Button>
                                    </div>
                                    <p className="text-muted-foreground text-sm">Click to select a different file</p>
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    <Upload className="text-muted-foreground mx-auto h-12 w-12" />
                                    <div>
                                        <p className="text-lg font-medium">
                                            {isDragActive ? 'Drop your PDF here' : 'Upload PDF Document'}
                                        </p>
                                        <p className="text-muted-foreground text-sm">
                                            Drag and drop a PDF file here, or click to select
                                        </p>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Form */}
                        <Form {...form}>
                            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                                <FormField
                                    control={form.control}
                                    name="name"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Document Name</FormLabel>
                                            <FormControl>
                                                <Input
                                                    placeholder="Enter document name..."
                                                    {...field}
                                                    disabled={uploadDocumentMutation.isPending}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <div className="flex items-center space-x-4">
                                    <Button type="submit" disabled={uploadDocumentMutation.isPending || !selectedFile}>
                                        {uploadDocumentMutation.isPending && (
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        )}
                                        Upload Document
                                    </Button>
                                    <Button type="button" variant="outline" asChild>
                                        <Link href={`/admin/projects/${projectId}/categories/${categoryId}`}>
                                            Cancel
                                        </Link>
                                    </Button>
                                </div>
                            </form>
                        </Form>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
