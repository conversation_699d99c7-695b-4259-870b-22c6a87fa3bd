'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import useMutationHelper from '@/hooks/use-mutation-helper';
import { useCreateCategory } from '@/modules/category';
import { CreateCategorySchema, createCategorySchema } from '@/modules/category/schema';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';

export default function CreateCategoryPage() {
    const params = useParams();
    const router = useRouter();
    const projectId = params.projectId as string;
    const createCategoryMutation = useCreateCategory();

    const form = useForm<CreateCategorySchema>({
        resolver: zodResolver(createCategorySchema),
        defaultValues: {
            name: '',
            projectId,
        },
    });

    useMutationHelper({
        mutation: createCategoryMutation,
        onSuccess: () => {
            router.push(`/admin/projects/${projectId}`);
        },
        successMessage: 'Category created successfully',
        errorMessage: 'Failed to create category',
        showSuccessToast: true,
        showErrorToast: true,
    });

    const onSubmit = (data: CreateCategorySchema) => {
        createCategoryMutation.mutate({ ...data, projectId });
    };

    return (
        <div className="space-y-6 p-6">
            <div className="flex items-center space-x-4">
                <Button variant="outline" size="sm" asChild>
                    <Link href={`/admin/projects/${projectId}`}>
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Project
                    </Link>
                </Button>
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Create Category</h1>
                    <p className="text-muted-foreground">Create a new category to organize your documents</p>
                </div>
            </div>

            <div className="max-w-2xl">
                <Card>
                    <CardHeader>
                        <CardTitle>Category Details</CardTitle>
                        <CardDescription>Enter the basic information for your new category.</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Form {...form}>
                            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                                <FormField
                                    control={form.control}
                                    name="name"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Category Name</FormLabel>
                                            <FormControl>
                                                <Input placeholder="Enter category name..." {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <div className="flex items-center space-x-4">
                                    <Button type="submit" disabled={createCategoryMutation.isPending}>
                                        {createCategoryMutation.isPending && (
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        )}
                                        Create Category
                                    </Button>
                                    <Button type="button" variant="outline" asChild>
                                        <Link href={`/admin/projects/${projectId}`}>Cancel</Link>
                                    </Button>
                                </div>
                            </form>
                        </Form>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
