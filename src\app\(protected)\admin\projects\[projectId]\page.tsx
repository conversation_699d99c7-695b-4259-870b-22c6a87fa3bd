'use client';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useCategories } from '@/modules/category';
import { CategorySchema } from '@/modules/category/schema';
import { formatDistanceToNow } from 'date-fns';
import { ArrowLeft, FolderOpen, Plus } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

function CategoryCard({ category, projectId }: { category: CategorySchema; projectId: string }) {
    return (
        <Card className="transition-shadow hover:shadow-md">
            <CardHeader>
                <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                        <div className="bg-primary/10 rounded-lg p-2">
                            <FolderOpen className="text-primary h-5 w-5" />
                        </div>
                        <div>
                            <CardTitle className="text-lg">{category.name}</CardTitle>
                            <CardDescription>
                                Created {formatDistanceToNow(new Date(category.createdAt), { addSuffix: true })}
                            </CardDescription>
                        </div>
                    </div>
                    <Button asChild size="sm" variant="outline">
                        <Link href={`/admin/projects/${projectId}/categories/${category._id}`}>View</Link>
                    </Button>
                </div>
            </CardHeader>
        </Card>
    );
}

function CategorySkeleton() {
    return (
        <Card>
            <CardHeader>
                <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                        <Skeleton className="h-9 w-9 rounded-lg" />
                        <div className="space-y-2">
                            <Skeleton className="h-5 w-32" />
                            <Skeleton className="h-4 w-24" />
                        </div>
                    </div>
                    <Skeleton className="h-8 w-16" />
                </div>
            </CardHeader>
        </Card>
    );
}

export default function ProjectDetailPage() {
    const params = useParams();
    const projectId = params.projectId as string;

    const { data: categoriesData, isLoading, error } = useCategories(projectId);

    return (
        <div className="space-y-6 p-6">
            <div className="flex items-center space-x-4">
                <Button variant="outline" size="sm" asChild>
                    <Link href="/admin/projects">
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Projects
                    </Link>
                </Button>
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Project Details</h1>
                    <p className="text-muted-foreground">Manage categories and documents for this project</p>
                </div>
            </div>

            <div className="grid gap-6">
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle>Categories</CardTitle>
                                <CardDescription>Organize your documents into categories</CardDescription>
                            </div>
                            <Button asChild>
                                <Link href={`/admin/projects/${projectId}/categories/create`}>
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add Category
                                </Link>
                            </Button>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {error && (
                            <div className="py-8 text-center">
                                <p className="text-destructive">Failed to load categories. Please try again.</p>
                            </div>
                        )}

                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                            {isLoading ? (
                                Array.from({ length: 3 }).map((_, i) => <CategorySkeleton key={i} />)
                            ) : categoriesData?.data.categories.length === 0 ? (
                                <div className="col-span-full py-8 text-center">
                                    <div className="space-y-4">
                                        <div className="bg-muted/50 inline-block rounded-lg p-4">
                                            <FolderOpen className="text-muted-foreground h-8 w-8" />
                                        </div>
                                        <div>
                                            <h3 className="text-lg font-semibold">No categories yet</h3>
                                            <p className="text-muted-foreground mb-4">
                                                Create your first category to organize documents.
                                            </p>
                                            <Button asChild>
                                                <Link href={`/admin/projects/${projectId}/categories/create`}>
                                                    <Plus className="mr-2 h-4 w-4" />
                                                    Create Category
                                                </Link>
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                categoriesData?.data.categories.map((category) => (
                                    <CategoryCard key={category._id} category={category} projectId={projectId} />
                                ))
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
