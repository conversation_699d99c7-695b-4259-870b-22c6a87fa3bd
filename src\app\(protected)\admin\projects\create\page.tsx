'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import useMutationHelper from '@/hooks/use-mutation-helper';
import { useCreateProject } from '@/modules/project';
import { CreateProjectSchema, createProjectSchema } from '@/modules/project/schema';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';

export default function CreateProjectPage() {
    const router = useRouter();
    const createProjectMutation = useCreateProject();

    const form = useForm<CreateProjectSchema>({
        resolver: zodResolver(createProjectSchema),
        defaultValues: {
            name: '',
        },
    });

    useMutationHelper({
        mutation: createProjectMutation,
        onSuccess: () => {
            router.push('/admin/projects');
        },
        successMessage: 'Project created successfully',
        errorMessage: 'Failed to create project',
        showSuccessToast: true,
        showErrorToast: true,
    });

    const onSubmit = (data: CreateProjectSchema) => {
        createProjectMutation.mutate(data);
    };

    return (
        <div className="space-y-6 p-6">
            <div className="flex items-center space-x-4">
                <Button variant="outline" size="sm" asChild>
                    <Link href="/admin/projects">
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Projects
                    </Link>
                </Button>
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Create Project</h1>
                    <p className="text-muted-foreground">Create a new PDF RAG project</p>
                </div>
            </div>

            <div className="max-w-2xl">
                <Card>
                    <CardHeader>
                        <CardTitle>Project Details</CardTitle>
                        <CardDescription>Enter the basic information for your new project.</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Form {...form}>
                            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                                <FormField
                                    control={form.control}
                                    name="name"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Project Name</FormLabel>
                                            <FormControl>
                                                <Input placeholder="Enter project name..." {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <div className="flex items-center space-x-4">
                                    <Button type="submit" disabled={createProjectMutation.isPending}>
                                        {createProjectMutation.isPending && (
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        )}
                                        Create Project
                                    </Button>
                                    <Button type="button" variant="outline" asChild>
                                        <Link href="/admin/projects">Cancel</Link>
                                    </Button>
                                </div>
                            </form>
                        </Form>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
