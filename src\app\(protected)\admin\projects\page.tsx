'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { useProjects } from '@/modules/project';
import { ProjectSchema } from '@/modules/project/schema';
import { formatDistanceToNow } from 'date-fns';
import { Plus, Search } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { useDebounce } from 'use-debounce';

function ProjectCard({ project }: { project: ProjectSchema }) {
    return (
        <Card className="transition-shadow hover:shadow-md">
            <CardHeader>
                <div className="flex items-start justify-between">
                    <div>
                        <CardTitle className="text-lg">{project.name}</CardTitle>
                        <CardDescription>
                            Created {formatDistanceToNow(new Date(project.createdAt), { addSuffix: true })}
                        </CardDescription>
                    </div>
                    <Button asChild size="sm" variant="outline">
                        <Link href={`/admin/projects/${project._id}`}>View</Link>
                    </Button>
                </div>
            </CardHeader>
        </Card>
    );
}

function ProjectSkeleton() {
    return (
        <Card>
            <CardHeader>
                <div className="flex items-start justify-between">
                    <div className="space-y-2">
                        <Skeleton className="h-5 w-32" />
                        <Skeleton className="h-4 w-24" />
                    </div>
                    <Skeleton className="h-8 w-16" />
                </div>
            </CardHeader>
        </Card>
    );
}

export default function ProjectsPage() {
    const [search, setSearch] = useState('');
    const [debouncedSearch] = useDebounce(search, 300);

    const { data, isLoading, error } = useProjects({
        search: debouncedSearch,
        limit: 20,
        offset: 0,
    });

    return (
        <div className="space-y-6 p-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Projects</h1>
                    <p className="text-muted-foreground">Manage your PDF RAG projects</p>
                </div>
                <Button asChild>
                    <Link href="/admin/projects/create">
                        <Plus className="mr-2 h-4 w-4" />
                        Create Project
                    </Link>
                </Button>
            </div>

            <div className="flex items-center space-x-2">
                <div className="relative max-w-sm flex-1">
                    <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
                    <Input
                        placeholder="Search projects..."
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}
                        className="pl-9"
                    />
                </div>
            </div>

            {error && (
                <Card className="border-destructive">
                    <CardContent className="pt-6">
                        <p className="text-destructive">Failed to load projects. Please try again.</p>
                    </CardContent>
                </Card>
            )}

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {isLoading ? (
                    Array.from({ length: 6 }).map((_, i) => <ProjectSkeleton key={i} />)
                ) : data?.data.projects.length === 0 ? (
                    <div className="col-span-full">
                        <Card>
                            <CardContent className="flex flex-col items-center justify-center py-12">
                                <div className="text-center">
                                    <h3 className="text-lg font-semibold">No projects found</h3>
                                    <p className="text-muted-foreground mb-4">
                                        {search
                                            ? 'No projects match your search.'
                                            : 'Get started by creating your first project.'}
                                    </p>
                                    <Button asChild>
                                        <Link href="/admin/projects/create">
                                            <Plus className="mr-2 h-4 w-4" />
                                            Create Project
                                        </Link>
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                ) : (
                    data?.data.projects.map((project) => <ProjectCard key={project._id} project={project} />)
                )}
            </div>

            {data?.data.projects && data.data.projects.length > 0 && (
                <div className="flex items-center justify-center pt-4">
                    <p className="text-muted-foreground text-sm">
                        Showing {data.data.projects.length} of {data.data.pagination.total} projects
                    </p>
                </div>
            )}
        </div>
    );
}
