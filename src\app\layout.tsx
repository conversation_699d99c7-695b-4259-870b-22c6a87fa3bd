import { cn } from '@/lib/utils';
import QueryProvider from '@/providers/query.provider';
import { ThemeProvider } from '@/providers/theme.provider';
import { LayoutProps } from '@/types/global';
import type { Metadata } from 'next';
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';
import { NuqsAdapter } from 'nuqs/adapters/next/app';
import { Toaster } from 'sonner';
import './globals.css';

const geistSans = Geist({
    variable: '--font-geist-sans',
    subsets: ['latin'],
});

const geistMono = Geist_Mono({
    variable: '--font-geist-mono',
    subsets: ['latin'],
});

export const metadata: Metadata = {
    title: 'PDF Rag',
};

export default function RootLayout({ children }: LayoutProps) {
    return (
        <html lang="en" suppressHydrationWarning>
            <body className={cn(geistSans.variable, geistMono.variable, 'antialiased')}>
                <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
                    <NuqsAdapter>
                        <QueryProvider>
                            {children}
                            <Toaster richColors closeButton />
                        </QueryProvider>
                    </NuqsAdapter>
                </ThemeProvider>
            </body>
        </html>
    );
}
