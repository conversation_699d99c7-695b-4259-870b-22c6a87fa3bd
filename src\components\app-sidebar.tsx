'use client';
import { NavUser } from '@/components/nav-user';
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
} from '@/components/ui/sidebar';
import { createRouteMatcher } from '@/lib/utils';
import { FolderOpen, LayoutDashboard, Package, ShoppingCart, Users, Warehouse } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const navigation = [
    { name: 'Dashboard', href: '/admin', icon: LayoutDashboard, matcher: createRouteMatcher(['/admin']) },
    {
        name: 'Projects',
        href: '/admin/projects',
        icon: FolderOpen,
        matcher: createRouteMatcher(['/admin/projects', '/admin/projects/(.*)']),
    },
    {
        name: 'Customers',
        href: '/admin/customers',
        icon: Users,
        matcher: createRouteMatcher(['/admin/customers']),
    },
    {
        name: 'Orders',
        href: '/admin/orders',
        icon: ShoppingCart,
        matcher: createRouteMatcher(['/admin/orders']),
    },
    {
        name: 'Products',
        href: '/admin/product',
        icon: Package,
        matcher: createRouteMatcher(['/admin/product', '/admin/product/(.*)']),
    },
    {
        name: 'Inventories',
        href: '/admin/inventory',
        icon: Warehouse,
        matcher: createRouteMatcher(['/admin/inventory', '/admin/inventory/(.*)']),
    },
];

export function AppSidebar() {
    const pathname = usePathname();
    return (
        <Sidebar variant="inset">
            <SidebarHeader />
            <SidebarContent>
                <SidebarGroup>
                    <SidebarGroupContent>
                        <SidebarMenu>
                            {navigation.map((item) => {
                                const isActive = item.matcher(pathname);
                                return (
                                    <SidebarMenuItem key={item.href}>
                                        <SidebarMenuButton asChild variant={isActive ? 'primary' : 'default'}>
                                            <Link href={item.href}>
                                                <item.icon />
                                                <span>{item.name}</span>
                                            </Link>
                                        </SidebarMenuButton>
                                    </SidebarMenuItem>
                                );
                            })}
                        </SidebarMenu>
                    </SidebarGroupContent>
                </SidebarGroup>
            </SidebarContent>
            <SidebarFooter>
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
