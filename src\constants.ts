export const BASE_URL = 'https://stationery-backend-3abw.onrender.com';

export const API_URL = {
    admin: {
        auth: {
            login: {
                url: '/api/admin/auth/login',
                method: 'post',
            },
        },
        product: {
            'list': {
                url: '/api/admin/product/list',
                method: 'post',
            },
            'get': {
                url: '/api/admin/product/get',
                method: 'post',
            },
            'create': {
                url: '/api/admin/product/create',
                method: 'post',
            },
            'update': {
                url: '/api/admin/product/update',
                method: 'post',
            },
            'delete': {
                url: '/api/admin/product/delete',
                method: 'post',
            },
            'delete-image': {
                url: '/api/admin/product/delete-image',
                method: 'post',
            },
        },
        inventory: {
            'list': {
                url: '/api/admin/inventory/list',
                method: 'post',
            },
            'get': {
                url: '/api/admin/inventory/get',
                method: 'post',
            },
            'create': {
                url: '/api/admin/inventory/create',
                method: 'post',
            },
            'update': {
                url: '/api/admin/inventory/update',
                method: 'post',
            },
            'bulk-update': {
                url: '/api/admin/inventory/bulk-update',
                method: 'post',
            },
            'delete': {
                url: '/api/admin/inventory/delete',
                method: 'post',
            },
        },
    },
    project: {
        list: {
            url: '/projects',
            method: 'get',
        },
        create: {
            url: '/v2/projects',
            method: 'post',
        },
    },
    category: {
        list: {
            url: '/projects/:projectId/category',
            method: 'get',
        },
        create: {
            url: '/v2/projects/:projectId/category',
            method: 'post',
        },
    },
    document: {
        list: {
            url: '/documents/:categoryId',
            method: 'get',
        },
        upload: {
            url: '/v2/documents/upload',
            method: 'post',
        },
    },
    chat: {
        health: {
            url: '/chat/health',
            method: 'get',
        },
        send: {
            url: '/v2/chat',
            method: 'post',
        },
    },
} as const;

