import { LoginResponse, LoginSchema } from '@/modules/auth/schema';
import { useAuthStore } from '@/store/auth.store';
import { useMutation } from '@tanstack/react-query';
import axios from 'axios';

export const login = async (data: LoginSchema): Promise<LoginResponse> => {
    return (
        await axios({
            url: '/api/auth/login',
            method: 'post',
            data,
        })
    ).data;
};

export const useAdminLogin = () => {
    const store = useAuthStore();
    return useMutation({
        mutationFn: login,
        onSuccess: (data) => {
            store.setUser({
                ...data.data.admin,
                ...data.data.tokens,
            });
        },
    });
};

export const logout = async () => {
    return (
        await axios({
            url: '/api/auth/logout',
            method: 'get',
        })
    ).data;
};

export const useLogout = () => {
    const store = useAuthStore();
    return useMutation({
        mutationFn: logout,
        onSuccess: () => {
            store.logout();
        },
    });
};
