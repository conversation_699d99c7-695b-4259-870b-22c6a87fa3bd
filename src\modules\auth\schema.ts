import z from 'zod';

export const loginSchema = z.object({
    email: z.email(),
    password: z.string().min(1),
});

export type Admin = {
    id: string;
    email: string;
    name: string;
    avatar: string;
    role: string;
};

export type Tokens = {
    accessToken: string;
    refreshToken: string;
};

export type LoginResponse = {
    data: {
        admin: Admin;
        tokens: Tokens;
    };
};

export type LoginSchema = {
    email: string;
    password: string;
};
