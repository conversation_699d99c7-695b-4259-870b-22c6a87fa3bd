import { axios } from '@/lib/axios';
import { CategoryListResponse, CreateCategorySchema } from '@/modules/category/schema';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

export const categoryList = async (projectId: string): Promise<CategoryListResponse> => {
    return (await axios.get(`/projects/${projectId}/category`)).data;
};

export const useCategories = (projectId: string) => {
    return useQuery({
        queryKey: ['category', projectId],
        queryFn: async () => await categoryList(projectId),
        enabled: !!projectId,
    });
};

export const createCategory = async (data: CreateCategorySchema & { projectId: string }) => {
    const { projectId, ...categoryData } = data;
    return (await axios.post(`/v2/projects/${projectId}/category`, categoryData)).data;
};

export const useCreateCategory = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: createCategory,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['category'],
            });
        },
    });
};
