import z from 'zod';

export const baseCategorySchema = z.object({
    _id: z.string().min(1, { message: 'is required' }),
    name: z.string().min(1, { message: 'name is required' }),
    projectId: z.string().min(1, { message: 'projectId is required' }),
    createdAt: z.string().min(1, { message: 'createdAt is required' }),
    updatedAt: z.string().min(1, { message: 'updatedAt is required' }),
});

export const createCategorySchema = baseCategorySchema.omit({
    _id: true,
    createdAt: true,
    updatedAt: true,
});

export type CategorySchema = z.infer<typeof baseCategorySchema>;
export type CreateCategorySchema = z.infer<typeof createCategorySchema>;

export type CategoryListResponse = {
    success: boolean;
    data: {
        categories: CategorySchema[];
    };
};
