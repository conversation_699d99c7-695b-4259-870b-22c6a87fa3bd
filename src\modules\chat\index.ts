import { axios } from '@/lib/axios';
import { ChatRequestSchema, ChatResponseSchema } from '@/modules/chat/schema';
import { useMutation } from '@tanstack/react-query';

export const chatHealth = async (): Promise<{ success: boolean }> => {
    return (await axios.get('/chat/health')).data;
};

export const sendChatMessage = async (data: ChatRequestSchema): Promise<ChatResponseSchema> => {
    return (await axios.post('/v2/chat', data)).data;
};

export const useChatMessage = () => {
    return useMutation({
        mutationFn: sendChatMessage,
    });
};

export const useChatHealth = () => {
    return useMutation({
        mutationFn: chatHealth,
    });
};
