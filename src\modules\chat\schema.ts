import z from 'zod';

export const chatRequestSchema = z.object({
    query: z.string().min(1, { message: 'query is required' }),
    projectId: z.number().min(1, { message: 'projectId is required' }),
    categoryId: z.number().min(1, { message: 'categoryId is required' }),
});

export const chatResponseSchema = z.object({
    success: z.boolean(),
    data: z.object({
        response: z.string(),
        sources: z.array(z.string()).optional(),
    }),
});

export type ChatRequestSchema = z.infer<typeof chatRequestSchema>;
export type ChatResponseSchema = z.infer<typeof chatResponseSchema>;