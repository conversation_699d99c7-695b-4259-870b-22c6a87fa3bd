import { axios } from '@/lib/axios';
import { DocumentListResponse, UploadDocumentSchema } from '@/modules/document/schema';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

export const documentList = async (categoryId: string): Promise<DocumentListResponse> => {
    return (await axios.get(`/documents/${categoryId}`)).data;
};

export const useDocuments = (categoryId: string) => {
    return useQuery({
        queryKey: ['document', categoryId],
        queryFn: async () => await documentList(categoryId),
        enabled: !!categoryId,
    });
};

export const uploadDocument = async (data: UploadDocumentSchema) => {
    const formData = new FormData();
    formData.append('projectId', data.projectId);
    formData.append('categoryId', data.categoryId);
    formData.append('file', data.file);
    if (data.metadata) {
        formData.append('metadata', JSON.stringify(data.metadata));
    }

    return (await axios.post('/v2/documents/upload', formData)).data;
};

export const useUploadDocument = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: uploadDocument,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['document'],
            });
        },
    });
};
