import z from 'zod';

export const baseDocumentSchema = z.object({
    _id: z.string().min(1, { message: 'is required' }),
    name: z.string().min(1, { message: 'name is required' }),
    projectId: z.string().min(1, { message: 'projectId is required' }),
    categoryId: z.string().min(1, { message: 'categoryId is required' }),
    url: z.string().min(1, { message: 'url is required' }),
    metadata: z.object(z.any()).optional(),
    createdAt: z.string().min(1, { message: 'createdAt is required' }),
    updatedAt: z.string().min(1, { message: 'updatedAt is required' }),
});

export const uploadDocumentSchema = z.object({
    projectId: z.string().min(1, { message: 'projectId is required' }),
    categoryId: z.string().min(1, { message: 'categoryId is required' }),
    file: z.instanceof(File),
    metadata: z.object(z.any()).optional(),
});

export type DocumentSchema = z.infer<typeof baseDocumentSchema>;
export type UploadDocumentSchema = z.infer<typeof uploadDocumentSchema>;

export type DocumentListResponse = {
    success: boolean;
    data: {
        documents: DocumentSchema[];
    };
};
