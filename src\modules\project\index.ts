import { API_URL } from '@/constants';
import { axios } from '@/lib/axios';
import { ProjectListResponse, CreateProjectSchema } from '@/modules/project/schema';
import { FilterSchema } from '@/types/global';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

export const projectList = async (data: FilterSchema): Promise<ProjectListResponse> => {
    return (await axios.get('/projects', { params: data })).data;
};

export const useProjects = (data: Partial<FilterSchema>) => {
    const { limit = 10, offset = 0, search } = data;
    return useQuery({
        queryKey: ['project', data],
        queryFn: async () => await projectList({ limit, offset, search }),
    });
};

export const createProject = async (data: CreateProjectSchema) => {
    return (await axios.post('/v2/projects', data)).data;
};

export const useCreateProject = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: createProject,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['project'],
            });
        },
    });
};