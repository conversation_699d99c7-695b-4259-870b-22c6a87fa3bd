import { ApiPagination } from '@/types/global';
import z from 'zod';

export const baseProjectSchema = z.object({
    _id: z.string().min(1, { message: 'is required' }),
    name: z.string().min(1, { message: 'name is required' }),
    createdAt: z.string().min(1, { message: 'createdAt is required' }),
    updatedAt: z.string().min(1, { message: 'updatedAt is required' }),
});

export const createProjectSchema = baseProjectSchema.omit({
    _id: true,
    createdAt: true,
    updatedAt: true,
});

export type ProjectSchema = z.infer<typeof baseProjectSchema>;
export type CreateProjectSchema = z.infer<typeof createProjectSchema>;

export type ProjectListResponse = {
    success: boolean;
    data: {
        pagination: ApiPagination;
        projects: ProjectSchema[];
    };
};