import { Admin, Tokens } from '@/modules/auth/schema';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type AdminUser = Admin & Tokens;

type AuthStore = {
    user: AdminUser | null;
    isAuth: boolean;
    setUser: (user: AdminUser) => void;
    logout: () => void;
};

export const useAuthStore = create<AuthStore>()(
    persist(
        (set) => ({
            user: null,
            isAuth: false,
            setUser: (user: AdminUser) => set({ user, isAuth: true }),
            logout: () => set({ user: null, isAuth: false }),
        }),
        {
            name: 'dashboard-auth-store',
        },
    ),
);
